# 数据库架构说明

本项目使用 Drizzle ORM 与 Cloudflare D1 数据库，为软件许可证验证服务提供数据存储。

## 数据表结构

### 1. users - 用户表

存储系统用户信息，包括管理员和分销商。

**字段说明：**

- `id`: 主键，自增
- `username`: 用户名，唯一
- `passwordHash`: 密码哈希
- `role`: 用户角色 ('ADMIN' | 'DISTRIBUTOR')
- `status`: 用户状态 ('ACTIVE' | 'INACTIVE' | 'SUSPENDED')
- `nickName`: 昵称
- `wechat`: 微信号
- `avatar`: 头像 URL
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

### 2. products - 产品表

存储软件产品信息。

**字段说明：**

- `id`: 主键，自增
- `name`: 产品名称，唯一
- `description`: 产品描述
- `category`: 产品分类
- `status`: 产品状态 ('ACTIVE' | 'INACTIVE')
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

### 3. product_versions - 产品版本表

存储产品的不同版本信息。

**字段说明：**

- `id`: 主键，自增
- `productId`: 关联产品 ID
- `version`: 版本号
- `versionName`: 版本名称
- `description`: 版本描述
- `configTemplate`: 配置模板（JSON 格式）
- `encryptionKey`: 加密密钥
- `defaultPrice`: 默认价格
- `downloadLink`: 下载链接
- `coverUrl`: 封面图片 URL
- `changelog`: 更新日志
- `status`: 版本状态 ('ACTIVE' | 'INACTIVE')
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

### 4. authorizations - 授权表

管理分销商对产品版本的销售授权。

**字段说明：**

- `id`: 主键，自增
- `distributorId`: 分销商 ID
- `versionId`: 产品版本 ID
- `customPrice`: 自定义价格（可选）
- `status`: 授权状态 ('ACTIVE' | 'INACTIVE')
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

### 5. licenses - 许可证表

存储生成的许可证信息。

**字段说明：**

- `id`: 主键，自增
- `versionId`: 产品版本 ID
- `licenseKey`: 许可证密钥，唯一
- `status`: 许可证状态 ('ACTIVE' | 'INACTIVE' | 'EXPIRED' | 'REVOKED')
- `verifyConfig`: 验证配置（JSON 格式）
- `activatedAt`: 激活时间
- `distributorId`: 分销商 ID
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

## 使用方法

### 1. 安装依赖

```bash
pnpm install
```

### 2. 配置环境变量

复制 `.env.example` 到 `.env` 并填入正确的配置：

```bash
cp .env.example .env
```

### 3. 生成迁移文件

```bash
pnpm db:generate
```

### 4. 应用迁移

```bash
pnpm db:migrate
```

### 5. 推送架构到数据库（开发环境）

```bash
pnpm db:push
```

### 6. 启动 Drizzle Studio（可选）

```bash
pnpm db:studio
```

## 代码示例

### 基本使用

```typescript
import { createDb, createOperations } from "./db";

// 在 Cloudflare Worker 中使用
export default {
  async fetch(request: Request, env: Env) {
    const db = createDb(env.DB);
    const ops = createOperations(db);

    // 创建用户
    const user = await ops.users.createUser({
      username: "admin",
      passwordHash: "hashed_password",
      role: "ADMIN",
      status: "ACTIVE",
    });

    // 查找用户
    const foundUser = await ops.users.findUserByUsername("admin");

    return Response.json({ user: foundUser });
  },
};
```

### 许可证验证示例

```typescript
// 验证许可证
const license = await ops.licenses.findLicenseByKey("LICENSE_KEY_HERE");
if (license && license.license.status === "ACTIVE") {
  // 许可证有效
  return Response.json({ valid: true, product: license.product });
} else {
  // 许可证无效
  return Response.json({ valid: false }, { status: 401 });
}
```

## 索引策略

数据库包含了优化的索引策略：

1. **唯一索引**: 确保数据唯一性（用户名、产品名、许可证密钥等）
2. **单列索引**: 优化常用查询字段（状态、角色、时间等）
3. **复合索引**: 优化多条件查询（角色+状态、分销商+状态等）

## 注意事项

1. **时间字段**: 使用 ISO 8601 格式的字符串存储时间
2. **JSON 字段**: `configTemplate` 和 `verifyConfig` 存储为 JSON 字符串
3. **外键约束**: 使用适当的级联删除和更新策略
4. **状态管理**: 所有实体都有状态字段，支持软删除模式
