# 技术规格文档 - Backend API Architecture

## 问题陈述
- **业务问题**: 需要实现软件许可证验证和配置管理的核心API功能，为客户端提供配置获取和更新能力。
- **当前状态**: 项目已有完整的数据库架构设计和加密服务，但核心API功能尚未实现，需要先实现核心验证API，后续再添加认证机制。
- **预期结果**: 客户端能够通过许可证密钥获取加密配置信息，并能更新配置数据，所有通信使用AES-256-CBC加密确保安全性。

## 解决方案概述
- **方法**: 使用Cloudflare Workers + Hono框架实现REST API，结合D1数据库和Drizzle ORM进行数据操作，利用现有CryptoService进行数据加密/解密。
- **核心改动**: 实现`GET /api/config/{licenseKey}`和`PUT /api/config/{licenseKey}`两个核心端点，包括数据库查询、许可证验证、配置模板处理和加密通信。
- **成功标准**: 端点能够正确处理许可证验证、配置获取/更新，返回标准化响应格式，支持AES-256-CBC加密通信，具备完善的错误处理机制。

## 技术实现

### 数据库操作接口设计
- **查询表**: `licenses` → `product_versions` → `products`
- **关键字段**: 
  - `licenses.licenseKey` - 许可证密钥验证
  - `licenses.status` - 许可证状态检查
  - `licenses.verifyConfig` - 客户端配置存储
  - `product_versions.configTemplate` - 配置模板
  - `product_versions.encryptionKey` - 加密密钥
- **查询流程**: 
  1. 通过licenseKey查询licenses表
  2. 验证许可证状态（ACTIVE/INACTIVE/REVOKED）
  3. 关联查询product_versions获取配置模板和加密密钥
  4. 合并配置模板与客户端配置

### API端点规范

#### GET /api/config/{licenseKey} - 获取加密配置
**功能**: 获取指定许可证的加密配置信息

**请求参数**:
```typescript
path: {
  licenseKey: string; // 许可证密钥
}
```

**响应格式**:
```typescript
{
  success: boolean;
  data?: {
    configTemplate: any; // 配置模板JSON
    verifyConfig: any;   // 客户端配置JSON
    version: {
      id: number;
      version: string;
      versionName: string;
    };
    product: {
      id: number;
      name: string;
      description: string;
    };
  };
  error?: {
    code: number;
    message: string;
    details?: string;
  };
  timestamp: number;
}
```

#### PUT /api/config/{licenseKey} - 更新加密配置
**功能**: 更新指定许可证的客户端配置

**请求参数**:
```typescript
path: {
  licenseKey: string; // 许可证密钥
}
body: {
  verifyConfig: any; // 客户端配置JSON对象
}
```

**响应格式**:
```typescript
{
  success: boolean;
  data?: {
    updated: boolean;
    verifyConfig: any;
  };
  error?: {
    code: number;
    message: string;
    details?: string;
  };
  timestamp: number;
}
```

### 加密/解密流程实现

#### 客户端请求加密处理
```typescript
import { cryptoService } from '../utils/crypto';

// 加密请求数据
const encryptRequest = (data: any, encryptionKey: string): string => {
  const jsonStr = JSON.stringify(data);
  return cryptoService.encrypt(jsonStr, encryptionKey);
};

// 解密响应数据
const decryptResponse = (encryptedData: string, encryptionKey: string): any => {
  const decrypted = cryptoService.decrypt(encryptedData, encryptionKey);
  return JSON.parse(decrypted);
};

// 获取加密密钥的流程
const getEncryptionKey = async (licenseKey: string): Promise<string> => {
  // 1. 查询许可证
  const license = await db.query.licenses.findFirst({
    where: eq(licenses.licenseKey, licenseKey),
    with: {
      version: {
        with: {
          product: true
        }
      }
    }
  });
  
  if (!license) {
    throw new Error('许可证不存在');
  }
  
  // 2. 检查许可证状态
  if (license.status !== 'ACTIVE') {
    throw new Error('许可证状态无效');
  }
  
  // 3. 返回加密密钥
  return license.version.encryptionKey;
};
```

### 错误处理机制

#### 错误类型定义
```typescript
export enum ErrorCode {
  LICENSE_NOT_FOUND = 1001,
  LICENSE_INVALID = 1002,
  LICENSE_EXPIRED = 1003,
  LICENSE_REVOKED = 1004,
  ENCRYPTION_ERROR = 2001,
  DATABASE_ERROR = 3001,
  VALIDATION_ERROR = 4001,
  INTERNAL_ERROR = 5001
}

export class APIError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public details?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export const Errors = {
  LICENSE_NOT_FOUND: new APIError(ErrorCode.LICENSE_NOT_FOUND, '许可证不存在'),
  LICENSE_INVALID: new APIError(ErrorCode.LICENSE_INVALID, '许可证状态无效'),
  LICENSE_EXPIRED: new APIError(ErrorCode.LICENSE_EXPIRED, '许可证已过期'),
  LICENSE_REVOKED: new APIError(ErrorCode.LICENSE_REVOKED, '许可证已被撤销'),
  ENCRYPTION_ERROR: new APIError(ErrorCode.ENCRYPTION_ERROR, '加密/解密失败'),
  DATABASE_ERROR: new APIError(ErrorCode.DATABASE_ERROR, '数据库操作失败'),
  VALIDATION_ERROR: new APIError(ErrorCode.VALIDATION_ERROR, '请求数据验证失败'),
  INTERNAL_ERROR: new APIError(ErrorCode.INTERNAL_ERROR, '内部服务器错误')
};
```

#### 错误处理中间件
```typescript
import { Hono } from 'hono';
import { APIError, Errors } from '../errors';

export const errorHandler = (error: Error, c: any) => {
  console.error('API错误:', error);
  
  if (error instanceof APIError) {
    return c.json({
      success: false,
      error: {
        code: error.code,
        message: error.message,
        details: error.details
      },
      timestamp: Date.now()
    }, 400);
  }
  
  // 未知错误
  return c.json({
    success: false,
    error: {
      code: 5001,
      message: '内部服务器错误',
      details: error.message
    },
    timestamp: Date.now()
  }, 500);
};
```

### 响应格式标准化

#### 统一响应工具函数
```typescript
export const createResponse = <T>(data: T, success = true) => {
  return {
    success,
    data,
    timestamp: Date.now()
  };
};

export const createErrorResponse = (code: number, message: string, details?: string) => {
  return {
    success: false,
    error: {
      code,
      message,
      details
    },
    timestamp: Date.now()
  };
};

export const successResponse = <T>(data: T) => {
  return createResponse(data, true);
};

export const errorResponse = (code: number, message: string, details?: string) => {
  return createErrorResponse(code, message, details);
};
```

### 代码组织结构

#### 文件结构
```
src/
├── index.ts                    # 主入口文件
├── routes/
│   ├── api/
│   │   ├── config.ts          # 配置API路由
│   │   └── index.ts           # API路由汇总
│   └── index.ts               # 路由主入口
├── utils/
│   ├── crypto.ts              # 加密服务（已存在）
│   ├── response.ts            # 响应格式化工具
│   └── validation.ts          # 验证工具
├── errors/
│   └── index.ts               # 错误定义和处理
├── types/
│   └── api.ts                 # API类型定义
└── db/
    ├── index.ts               # 数据库连接（已存在）
    └── schema.ts              # 数据库模式（已存在）
```

#### 具体文件实现

**src/types/api.ts**
```typescript
export interface GetConfigResponse {
  configTemplate: any;
  verifyConfig: any;
  version: {
    id: number;
    version: string;
    versionName: string;
  };
  product: {
    id: number;
    name: string;
    description: string;
  };
}

export interface UpdateConfigRequest {
  verifyConfig: any;
}

export interface UpdateConfigResponse {
  updated: boolean;
  verifyConfig: any;
}

export interface StandardResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: number;
    message: string;
    details?: string;
  };
  timestamp: number;
}
```

**src/utils/response.ts**
```typescript
import { StandardResponse } from '../types/api';

export const createResponse = <T>(data: T, success = true): StandardResponse<T> => {
  return {
    success,
    data,
    timestamp: Date.now()
  };
};

export const createErrorResponse = (code: number, message: string, details?: string): StandardResponse<null> => {
  return {
    success: false,
    error: {
      code,
      message,
      details
    },
    timestamp: Date.now()
  };
};

export const successResponse = <T>(data: T): StandardResponse<T> => {
  return createResponse(data, true);
};

export const errorResponse = (code: number, message: string, details?: string): StandardResponse<null> => {
  return createErrorResponse(code, message, details);
};
```

**src/routes/api/config.ts**
```typescript
import { Hono } from 'hono';
import { eq } from 'drizzle-orm';
import { db } from '../../db';
import { licenses, productVersions, products } from '../../db/schema';
import { cryptoService } from '../../utils/crypto';
import { 
  GetConfigResponse, 
  UpdateConfigRequest, 
  UpdateConfigResponse 
} from '../../types/api';
import { createResponse, errorResponse, successResponse } from '../../utils/response';
import { APIError, Errors } from '../../errors';

const router = new Hono<{ Bindings: Env }>();

// GET /api/config/{licenseKey} - 获取加密配置
router.get('/:licenseKey', async (c) => {
  const { licenseKey } = c.req.param();

  try {
    // 1. 查询许可证及相关信息
    const license = await db.query.licenses.findFirst({
      where: eq(licenses.licenseKey, licenseKey),
      with: {
        version: {
          with: {
            product: true
          }
        }
      }
    });

    if (!license) {
      throw Errors.LICENSE_NOT_FOUND;
    }

    // 2. 检查许可证状态
    if (license.status !== 'ACTIVE') {
      throw Errors.LICENSE_INVALID;
    }

    // 3. 解析配置模板和客户端配置
    const configTemplate = JSON.parse(license.version.configTemplate);
    const verifyConfig = license.verifyConfig ? JSON.parse(license.verifyConfig) : {};

    // 4. 构建响应数据
    const response: GetConfigResponse = {
      configTemplate,
      verifyConfig,
      version: {
        id: license.version.id,
        version: license.version.version,
        versionName: license.version.versionName || ''
      },
      product: {
        id: license.version.product.id,
        name: license.version.product.name,
        description: license.version.product.description || ''
      }
    };

    // 5. 加密响应数据
    const responseData = successResponse(response);
    const encryptedData = cryptoService.encrypt(
      JSON.stringify(responseData), 
      license.version.encryptionKey
    );

    return c.json({ encryptedData }, 200);

  } catch (error) {
    if (error instanceof APIError) {
      return c.json(errorResponse(error.code, error.message, error.details), 400);
    }
    
    return c.json(errorResponse(5001, '内部服务器错误', error instanceof Error ? error.message : ''), 500);
  }
});

// PUT /api/config/{licenseKey} - 更新加密配置
router.put('/:licenseKey', async (c) => {
  const { licenseKey } = c.req.param();
  
  try {
    // 1. 获取加密密钥和验证许可证
    const license = await db.query.licenses.findFirst({
      where: eq(licenses.licenseKey, licenseKey),
      with: {
        version: true
      }
    });

    if (!license) {
      throw Errors.LICENSE_NOT_FOUND;
    }

    if (license.status !== 'ACTIVE') {
      throw Errors.LICENSE_INVALID;
    }

    // 2. 解密请求数据
    const encryptedBody = await c.req.json();
    const decryptedBody = cryptoService.decrypt(
      encryptedBody.encryptedData, 
      license.version.encryptionKey
    );
    
    const { verifyConfig }: UpdateConfigRequest = JSON.parse(decryptedBody);

    // 3. 验证请求数据
    if (!verifyConfig || typeof verifyConfig !== 'object') {
      throw new Error('无效的配置数据');
    }

    // 4. 更新许可证配置
    await db.update(licenses)
      .set({
        verifyConfig: JSON.stringify(verifyConfig),
        updatedAt: new Date().toISOString()
      })
      .where(eq(licenses.id, license.id));

    // 5. 构建响应
    const response: UpdateConfigResponse = {
      updated: true,
      verifyConfig
    };

    const responseData = successResponse(response);
    const encryptedResponse = cryptoService.encrypt(
      JSON.stringify(responseData), 
      license.version.encryptionKey
    );

    return c.json({ encryptedData: encryptedResponse }, 200);

  } catch (error) {
    if (error instanceof APIError) {
      return c.json(errorResponse(error.code, error.message, error.details), 400);
    }
    
    return c.json(errorResponse(5001, '内部服务器错误', error instanceof Error ? error.message : ''), 500);
  }
});

export { router as configRouter };
```

**src/routes/api/index.ts**
```typescript
import { Hono } from 'hono';
import { configRouter } from './config';

const router = new Hono<{ Bindings: Env }>();

// 挂载API路由
router.route('/config', configRouter);

export { router as apiRouter };
```

**src/routes/index.ts**
```typescript
import { Hono } from 'hono';
import { apiRouter } from './api';
import { errorHandler } from '../errors';

const router = new Hono<{ Bindings: Env }>();

// 挂载API路由组
router.route('/api', apiRouter);

// 错误处理中间件
router.onError(errorHandler);

export { router as appRouter };
```

**src/index.ts** (主入口文件)
```typescript
import { Hono } from 'hono';
import { appRouter } from './routes';

// 创建应用实例
const app = new Hono<{ Bindings: Env }>();

// 挂载所有路由
app.route('/', appRouter);

// 健康检查
app.get('/health', (c) => {
  return c.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString() 
  });
});

// 404处理
app.notFound((c) => {
  return c.json({ 
    success: false, 
    error: { 
      code: 404, 
      message: '端点不存在' 
    }, 
    timestamp: Date.now() 
  }, 404);
});

export default app;
```

## 实现序列

### 第一阶段：核心验证API实现

#### 步骤1：创建类型定义和工具函数
**文件**: `src/types/api.ts`, `src/utils/response.ts`, `src/errors/index.ts`
**任务**:
1. 定义API请求/响应类型
2. 创建标准化响应工具函数
3. 实现错误定义和处理机制

#### 步骤2：实现核心配置API
**文件**: `src/routes/api/config.ts`
**任务**:
1. 实现`GET /api/config/{licenseKey}`端点
2. 实现`PUT /api/config/{licenseKey}`端点
3. 集成数据库查询和加密处理
4. 实现许可证状态验证逻辑

#### 步骤3：路由配置和错误处理
**文件**: `src/routes/api/index.ts`, `src/routes/index.ts`, `src/index.ts`
**任务**:
1. 配置路由组结构
2. 添加错误处理中间件
3. 实现健康检查和404处理
4. 更新主入口文件

#### 步骤4：数据库模式验证
**任务**:
1. 运行`pnpm run db:push`确保数据库模式最新
2. 验证表关系和索引配置
3. 创建测试数据用于API测试

#### 步骤5：开发和测试
**任务**:
1. 启动开发服务器：`pnpm run dev`
2. 使用curl或Postman测试API端点
3. 验证加密/解密功能
4. 测试各种错误场景

### 依赖项更新

#### package.json依赖需求
```json
{
  "dependencies": {
    "hono": "^4.8.10",
    "drizzle-orm": "^0.44.4"
  }
}
```

#### 环境变量需求
```bash
# 在wrangler.toml或环境变量中配置
JWT_SECRET=your-jwt-secret-here
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_DATABASE_ID=your-database-id
CLOUDFLARE_D1_TOKEN=your-d1-token
```

## 验证计划

### 单元测试
1. **许可证查询测试**: 验证不同状态许可证的处理逻辑
2. **加密/解密测试**: 验证CryptoService的AES-256-CBC功能
3. **响应格式测试**: 验证标准化响应格式生成
4. **错误处理测试**: 验证各种错误情况的处理

### 集成测试
1. **API端点测试**:
   ```bash
   # 测试获取配置
   curl -X GET http://localhost:8787/api/config/TEST-KEY-001
   
   # 测试更新配置
   curl -X PUT http://localhost:8787/api/config/TEST-KEY-001 \
   -H "Content-Type: application/json" \
   -d '{"verifyConfig": {"enabled": true, "settings": {"maxDevices": 5}}}'
   ```

2. **加密通信测试**: 验证客户端与服务端之间的加密通信

### 业务逻辑验证
1. **许可证验证**: 确保只允许ACTIVE状态的许可证进行操作
2. **配置模板合并**: 验证配置模板与客户端配置的正确合并
3. **数据完整性**: 确保加密通信数据完整性
4. **性能测试**: 验证API响应时间在合理范围内

### 测试数据准备
```sql
-- 创建测试产品
INSERT INTO products (name, description, status) VALUES 
('测试产品', '用于API测试的产品', 'ACTIVE');

-- 创建测试产品版本
INSERT INTO product_versions (productId, version, versionName, configTemplate, encryptionKey, defaultPrice, status) VALUES 
(1, '1.0.0', '初版', '{"maxDevices": 5, "expiryDays": 365}', 'test-encryption-key-123', 99.99, 'ACTIVE');

-- 创建测试许可证
INSERT INTO licenses (versionId, licenseKey, status, verifyConfig, distributorId) VALUES 
(1, 'TEST-KEY-001', 'ACTIVE', '{"userLimits": 3}', 1);
```

这个技术规格文档提供了完整的实现蓝图，确保能够直接用于代码生成，专注于第一阶段的核心许可证验证API功能。