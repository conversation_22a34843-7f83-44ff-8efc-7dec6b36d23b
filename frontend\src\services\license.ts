import { apiClient } from '../lib/client';
import type {
  License,
  LicenseDetail,
  CreateLicenseRequest,
  UpdateLicenseRequest,
  LicenseQueryParams,
  PaginatedResponse
} from '@/types/api';

export class LicenseService {
  // 获取许可证列表
  async getLicenses(params?: LicenseQueryParams): Promise<PaginatedResponse<License>> {
    const response = await apiClient.get<{
      licenses: License[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>('/v1/licenses', params);
    
    if (response.success && response.data) {
      return {
        data: response.data.licenses,
        pagination: response.data.pagination,
      };
    }
    
    throw new Error(response.message || '获取许可证列表失败');
  }

  // 生成许可证
  async createLicenses(licenseData: CreateLicenseRequest): Promise<{ licenses: string[] }> {
    const response = await apiClient.post<{ licenses: string[] }>('/v1/licenses', licenseData);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '生成许可证失败');
  }

  // 获取许可证详情
  async getLicense(id: number): Promise<LicenseDetail> {
    const response = await apiClient.get<LicenseDetail>(`/v1/licenses/${id}`);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取许可证信息失败');
  }

  // 更新许可证
  async updateLicense(id: number, updateData: UpdateLicenseRequest): Promise<void> {
    const response = await apiClient.put(`/v1/licenses/${id}`, updateData);
    
    if (!response.success) {
      throw new Error(response.message || '更新许可证失败');
    }
  }
}

export const licenseService = new LicenseService();