export enum ErrorCode {
  LICENSE_NOT_FOUND = 1001,
  LICENSE_INVALID = 1002,
  LICENSE_EXPIRED = 1003,
  LICENSE_REVOKED = 1004,
  ENCRYPTION_ERROR = 2001,
  DATABASE_ERROR = 3001,
  VALIDATION_ERROR = 4001,
  INTERNAL_ERROR = 5001
}

export class APIError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public details?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export const Errors = {
  LICENSE_NOT_FOUND: new APIError(ErrorCode.LICENSE_NOT_FOUND, '许可证不存在'),
  LICENSE_INVALID: new APIError(ErrorCode.LICENSE_INVALID, '许可证状态无效'),
  LICENSE_EXPIRED: new APIError(ErrorCode.LICENSE_EXPIRED, '许可证已过期'),
  LICENSE_REVOKED: new APIError(ErrorCode.LICENSE_REVOKED, '许可证已被撤销'),
  ENCRYPTION_ERROR: new APIError(ErrorCode.ENCRYPTION_ERROR, '加密/解密失败'),
  DATABASE_ERROR: new APIError(ErrorCode.DATABASE_ERROR, '数据库操作失败'),
  VALIDATION_ERROR: new APIError(ErrorCode.VALIDATION_ERROR, '请求数据验证失败'),
  INTERNAL_ERROR: new APIError(ErrorCode.INTERNAL_ERROR, '内部服务器错误')
};

export const errorHandler = (error: Error, c: any) => {
  console.error('API错误:', error);
  
  if (error instanceof APIError) {
    return c.json({
      success: false,
      error: {
        code: error.code,
        message: error.message,
        details: error.details
      },
      timestamp: Date.now()
    }, 400);
  }
  
  return c.json({
    success: false,
    error: {
      code: 5001,
      message: '内部服务器错误',
      details: error.message
    },
    timestamp: Date.now()
  }, 500);
};