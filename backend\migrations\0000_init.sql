CREATE TABLE `authorizations` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`distributorId` integer NOT NULL,
	`versionId` integer NOT NULL,
	`customPrice` real,
	`status` text DEFAULT 'ACTIVE' NOT NULL,
	`createdAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updatedAt` text NOT NULL,
	FOREIGN KEY (`distributorId`) REFERENCES `users`(`id`) ON UPDATE cascade ON DELETE cascade,
	FOREIGN KEY (`versionId`) REFERENCES `product_versions`(`id`) ON UPDATE cascade ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `authorizations_distributorId_idx` ON `authorizations` (`distributorId`);--> statement-breakpoint
CREATE INDEX `authorizations_versionId_idx` ON `authorizations` (`versionId`);--> statement-breakpoint
CREATE INDEX `authorizations_status_idx` ON `authorizations` (`status`);--> statement-breakpoint
CREATE INDEX `authorizations_createdAt_idx` ON `authorizations` (`createdAt`);--> statement-breakpoint
CREATE INDEX `authorizations_distributorId_status_idx` ON `authorizations` (`distributorId`,`status`);--> statement-breakpoint
CREATE UNIQUE INDEX `authorizations_distributorId_versionId_key` ON `authorizations` (`distributorId`,`versionId`);--> statement-breakpoint
CREATE TABLE `licenses` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`versionId` integer NOT NULL,
	`licenseKey` text NOT NULL,
	`status` text DEFAULT 'INACTIVE' NOT NULL,
	`verifyConfig` text,
	`activatedAt` text,
	`distributorId` integer NOT NULL,
	`createdAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updatedAt` text NOT NULL,
	FOREIGN KEY (`versionId`) REFERENCES `product_versions`(`id`) ON UPDATE cascade ON DELETE cascade,
	FOREIGN KEY (`distributorId`) REFERENCES `users`(`id`) ON UPDATE cascade ON DELETE restrict
);
--> statement-breakpoint
CREATE UNIQUE INDEX `licenses_licenseKey_key` ON `licenses` (`licenseKey`);--> statement-breakpoint
CREATE INDEX `licenses_licenseKey_idx` ON `licenses` (`licenseKey`);--> statement-breakpoint
CREATE INDEX `licenses_versionId_idx` ON `licenses` (`versionId`);--> statement-breakpoint
CREATE INDEX `licenses_distributorId_idx` ON `licenses` (`distributorId`);--> statement-breakpoint
CREATE INDEX `licenses_status_idx` ON `licenses` (`status`);--> statement-breakpoint
CREATE INDEX `licenses_activatedAt_idx` ON `licenses` (`activatedAt`);--> statement-breakpoint
CREATE INDEX `licenses_createdAt_idx` ON `licenses` (`createdAt`);--> statement-breakpoint
CREATE INDEX `licenses_distributorId_status_idx` ON `licenses` (`distributorId`,`status`);--> statement-breakpoint
CREATE INDEX `licenses_status_createdAt_idx` ON `licenses` (`status`,`createdAt`);--> statement-breakpoint
CREATE TABLE `product_versions` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`productId` integer NOT NULL,
	`version` text NOT NULL,
	`versionName` text,
	`description` text,
	`configTemplate` text NOT NULL,
	`encryptionKey` text NOT NULL,
	`defaultPrice` real NOT NULL,
	`downloadLink` text,
	`coverUrl` text,
	`changelog` text,
	`status` text DEFAULT 'ACTIVE' NOT NULL,
	`createdAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updatedAt` text NOT NULL,
	FOREIGN KEY (`productId`) REFERENCES `products`(`id`) ON UPDATE cascade ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `product_versions_productId_idx` ON `product_versions` (`productId`);--> statement-breakpoint
CREATE INDEX `product_versions_status_idx` ON `product_versions` (`status`);--> statement-breakpoint
CREATE INDEX `product_versions_createdAt_idx` ON `product_versions` (`createdAt`);--> statement-breakpoint
CREATE INDEX `product_versions_productId_status_idx` ON `product_versions` (`productId`,`status`);--> statement-breakpoint
CREATE UNIQUE INDEX `product_versions_productId_version_key` ON `product_versions` (`productId`,`version`);--> statement-breakpoint
CREATE TABLE `products` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`category` text,
	`status` text DEFAULT 'ACTIVE' NOT NULL,
	`createdAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updatedAt` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `products_name_key` ON `products` (`name`);--> statement-breakpoint
CREATE INDEX `products_status_idx` ON `products` (`status`);--> statement-breakpoint
CREATE INDEX `products_category_idx` ON `products` (`category`);--> statement-breakpoint
CREATE INDEX `products_createdAt_idx` ON `products` (`createdAt`);--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`username` text NOT NULL,
	`passwordHash` text NOT NULL,
	`role` text NOT NULL,
	`status` text DEFAULT 'ACTIVE' NOT NULL,
	`nickName` text,
	`wechat` text,
	`avatar` text,
	`createdAt` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updatedAt` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_username_key` ON `users` (`username`);--> statement-breakpoint
CREATE INDEX `users_role_idx` ON `users` (`role`);--> statement-breakpoint
CREATE INDEX `users_status_idx` ON `users` (`status`);--> statement-breakpoint
CREATE INDEX `users_createdAt_idx` ON `users` (`createdAt`);--> statement-breakpoint
CREATE INDEX `users_role_status_idx` ON `users` (`role`,`status`);