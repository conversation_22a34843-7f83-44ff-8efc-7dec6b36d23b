import { apiClient } from '../lib/client';
import type {
  Product,
  CreateProductRequest,
  UpdateProductRequest,
  ProductQueryParams,
  PaginatedResponse
} from '@/types/api';

export class ProductService {
  // 获取产品列表
  async getProducts(params?: ProductQueryParams): Promise<PaginatedResponse<Product>> {
    const response = await apiClient.get<{
      products: Product[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>('/products', params);
    
    if (response.success && response.data) {
      return {
        data: response.data.products,
        pagination: response.data.pagination,
      };
    }
    
    throw new Error(response.message || '获取产品列表失败');
  }

  // 创建产品
  async createProduct(productData: CreateProductRequest): Promise<{ id: number }> {
    const response = await apiClient.post<{ id: number }>('/products', productData);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '创建产品失败');
  }

  // 获取产品详情
  async getProduct(id: number): Promise<{ product: Product }> {
    const response = await apiClient.get<{ product: Product }>(`/products/${id}`);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取产品信息失败');
  }

  // 更新产品
  async updateProduct(id: number, updateData: UpdateProductRequest): Promise<void> {
    const response = await apiClient.put(`/products/${id}`, updateData);
    
    if (!response.success) {
      throw new Error(response.message || '更新产品失败');
    }
  }

  // 删除产品
  async deleteProduct(id: number): Promise<void> {
    const response = await apiClient.delete(`/products/${id}`);
    
    if (!response.success) {
      throw new Error(response.message || '删除产品失败');
    }
  }
}

export const productService = new ProductService();