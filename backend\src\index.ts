import { Hono } from 'hono';
import { appRouter } from './routes';

// 创建应用实例
const app = new Hono<{ Bindings: Env }>();

// 挂载所有路由
app.route('/', appRouter);

// 健康检查
app.get('/health', (c) => {
  return c.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString() 
  });
});

// 404处理
app.notFound((c) => {
  return c.json({ 
    success: false, 
    error: { 
      code: 404, 
      message: '端点不存在' 
    }, 
    timestamp: Date.now() 
  }, 404);
});

export default app;