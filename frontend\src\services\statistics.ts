import { apiClient } from '../lib/client';
import type {
  SalesStats,
  VerificationStats,
  DashboardStats,
  StatsQueryParams
} from '@/types/api';

export class StatisticsService {
  // 获取销售统计
  async getSalesStats(params?: StatsQueryParams): Promise<SalesStats> {
    const response = await apiClient.get<SalesStats>('/stats/sales', params);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取销售统计失败');
  }

  // 获取验证统计
  async getVerificationStats(params?: StatsQueryParams): Promise<VerificationStats> {
    const response = await apiClient.get<VerificationStats>('/stats/verification', params);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取验证统计失败');
  }

  // 获取仪表板统计
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await apiClient.get<DashboardStats>('/stats/dashboard');
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取仪表板统计失败');
  }
}

export const statisticsService = new StatisticsService();