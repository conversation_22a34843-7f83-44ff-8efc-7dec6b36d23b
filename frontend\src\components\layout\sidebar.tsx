import { Link, useLocation } from "react-router-dom"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useUIStore } from "@/stores/ui-store"
import { useAuthStore } from "@/stores/auth-store"
import { authService } from "@/services/auth"
import { handleApiError } from "@/services"
import {
  LayoutDashboard,
  Package,
  Key,
  ShoppingCart,
  Users,
  ChevronLeft,
  Shield,
  LogOut,
  User
} from "lucide-react"

interface SidebarProps {
  isOpen: boolean
  onToggle: () => void
  isMobile?: boolean
}

const menuItems = [
  {
    title: "仪表板",
    href: "/",
    icon: LayoutDashboard
  },
  {
    title: "产品管理",
    href: "/products",
    icon: Package
  },
  {
    title: "许可证管理",
    href: "/licenses",
    icon: Key
  },
  {
    title: "订单管理",
    href: "/orders",
    icon: ShoppingCart
  },
  {
    title: "人员管理",
    href: "/admins",
    icon: Users
  }
]

export function Sidebar({ isOpen, onToggle, isMobile = false }: SidebarProps) {
  const location = useLocation()
  const { toggleSidebar } = useUIStore()
  const { user } = useAuthStore()
  
  // 根据用户角色过滤菜单项
  const filteredMenuItems = menuItems.filter(item => {
    // 人员管理只对super角色显示
    if (item.href === '/admins' && user?.role !== 'super') {
      return false
    }
    return true
  })

  console.log('Current user:', user) // 调试信息

  const handleLogout = async () => {
    try {
      await authService.logout()
    } catch (error) {
      handleApiError(error)
    }
  }

  return (
    <div className={cn(
      "flex flex-col border-r bg-background transition-all duration-300 z-50",
      // 移动端时使用固定定位
      isMobile ? "fixed inset-y-0 left-0" : "relative",
      // 移动端展开时全宽，收起时隐藏；桌面端正常宽度切换
      isMobile 
        ? (isOpen ? "w-64" : "w-0 -translate-x-full")
        : (isOpen ? "w-64" : "w-16")
    )}>
      <div className="flex h-16 items-center justify-center border-b px-4">
        {/* 桌面端始终显示，移动端仅展开时显示logo */}
        {!isMobile || isOpen ? (
          <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-lg">
            <Shield className="h-5 w-5 text-primary-foreground" />
          </div>
        ) : null}
      </div>

      <ScrollArea className="flex-1 px-2 py-4">
        <nav className="space-y-2">
          {filteredMenuItems.map((item) => {
            const Icon = item.icon
            const isActive = location.pathname === item.href
            
            return (
              <Link key={item.href} to={item.href}>
                <Button
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start gap-3",
                    !isOpen && "justify-center px-2"
                  )}
                >
                  <Icon className="h-4 w-4 flex-shrink-0" />
                  {isOpen && <span>{item.title}</span>}
                </Button>
              </Link>
            )
          })}
        </nav>
      </ScrollArea>

      <div className="border-t p-2 space-y-2">
        {/* 桌面端始终显示，移动端仅展开时显示展开按钮 */}
        {!isMobile || isOpen ? (
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start gap-3",
              !isOpen && "justify-center px-2"
            )}
            onClick={toggleSidebar}
          >
            <ChevronLeft className={cn(
              "h-4 w-4 flex-shrink-0 transition-transform",
              !isOpen && "rotate-180"
            )} />
            {isOpen && <span>收起</span>}
          </Button>
        ) : null}
        
        {/* 桌面端始终显示，移动端仅展开时显示头像 */}
        {!isMobile || isOpen ? (
          <div className={cn(
            "flex items-center gap-3 p-2",
            !isOpen && "justify-center"
          )}>
            {isOpen ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-3 w-full justify-start p-2">
                    <Avatar className="h-8 w-8 bg-primary">
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        {user?.username?.charAt(0).toUpperCase() || '管'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col text-left">
                      <span className="text-sm font-medium">{user?.username || '管理员'}</span>
                      <span className="text-xs text-muted-foreground">
                        {user?.role === 'super' ? '超级管理员' : '管理员'}
                      </span>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" side="right">
                  <DropdownMenuItem>
                    <User className="mr-2 h-4 w-4" />
                    个人信息
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-destructive focus:text-destructive">
                    <LogOut className="mr-2 h-4 w-4" />
                    退出登录
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Avatar className="h-8 w-8 bg-primary">
                <AvatarFallback className="bg-primary text-primary-foreground">
                  {user?.username?.charAt(0).toUpperCase() || '管'}
                </AvatarFallback>
              </Avatar>
            )}
          </div>
        ) : null}
      </div>
    </div>
  )
}