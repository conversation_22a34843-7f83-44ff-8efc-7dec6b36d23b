# API 技术规范文档

## 1. 系统概述

### 1.1 系统定位

软件许可证验证服务的核心是一个**配置管理服务**，为客户端软件提供加密的许可证配置获取和更新功能。

### 1.2 核心功能

- **配置获取**：根据 License Key 获取加密的验证配置
- **配置更新**：根据 License Key 更新加密的验证配置
- **安全通信**：所有数据传输均采用 AES 加密

### 1.3 技术架构

- **运行环境**：Cloudflare Workers
- **数据库**：Cloudflare D1 (SQLite)
- **加密算法**：AES-256-CBC

## 2. 核心 API 规范

### 2.1 配置获取 API

#### 接口定义

```
GET /api/config/{licenseKey}
```

#### 请求参数

| 参数       | 类型   | 位置 | 必填 | 说明       |
| ---------- | ------ | ---- | ---- | ---------- |
| licenseKey | string | path | 是   | 许可证密钥 |

#### 请求示例

```http
GET /api/config/LIC-ABC123-DEF456-GHI789
Host: verify.example.com
Content-Type: application/json
```

#### 响应格式

```json
{
  "success": true,
  "data": {
    "encryptedConfig": "base64_encoded_encrypted_data",
    "timestamp": **********
  }
}
```

#### 响应说明

- `encryptedConfig`: AES 加密后的配置数据（Base64 编码）
- `timestamp`: 配置生成时间戳

### 2.2 配置更新 API

#### 接口定义

```
PUT /api/config/{licenseKey}
```

#### 请求参数

| 参数            | 类型   | 位置 | 必填 | 说明           |
| --------------- | ------ | ---- | ---- | -------------- |
| licenseKey      | string | path | 是   | 许可证密钥     |
| encryptedConfig | string | body | 是   | 加密的配置数据 |

#### 请求示例

```http
PUT /api/config/LIC-ABC123-DEF456-GHI789
Host: verify.example.com
Content-Type: application/json

{
  "encryptedConfig": "base64_encoded_encrypted_data"
}
```

#### 响应格式

```json
{
  "success": true,
  "data": {
    "encryptedConfig": "base64_encoded_encrypted_data",
    "timestamp": **********
  }
}
```

## 3. 数据格式规范

### 3.1 验证模板格式 (verifyTemplate)

验证模板存储在 ProductVersion 表中，格式为 JSON 数组：

```json
[
  {
    "key": "expiration",
    "type": "date",
    "default": "2024-12-31",
    "label": "过期时间"
  },
  {
    "key": "max_devices",
    "type": "number",
    "default": 10,
    "label": "最大设备数"
  },
  {
    "key": "features",
    "type": "array",
    "default": ["feature1"],
    "options": [
      { "value": "feature1", "label": "特性1" },
      { "value": "feature2", "label": "特性2" }
    ],
    "label": "启用功能"
  },
  {
    "key": "is_trial",
    "type": "boolean",
    "default": false,
    "label": "试用版本"
  },
  {
    "key": "custom_field",
    "type": "string",
    "default": "",
    "label": "自定义字段"
  }
]
```

### 3.2 验证配置格式 (verifyConfig)

验证配置存储在 License 表中，格式为 JSON 对象（从模板转换而来）：

```json
{
  "expiration": "2024-12-31",
  "max_devices": 5,
  "features": ["feature1", "feature2"],
  "is_trial": false,
  "custom_field": "custom value",
  "current_devices": ["device1", "device2"]
}
```

### 3.3 模板到配置的转换规则

分发商创建 License 时，系统自动将 verifyTemplate 转换为 verifyConfig：

1. **提取默认值**：使用模板中的`default`字段作为初始值
2. **生成字典**：将数组格式转换为键值对格式
3. **保留扩展性**：支持添加运行时字段（如`current_devices`）

转换示例：

```javascript
// 模板 -> 配置转换逻辑
function templateToConfig(template) {
  const config = {};
  template.forEach((item) => {
    config[item.key] = item.default;
  });
  return config;
}
```

## 4. 加密机制规范

### 4.1 加密算法

- **算法**：AES-256-CBC
- **密钥来源**：ProductVersion.encryptionKey
- **IV 生成**：每次加密生成随机 IV
- **编码格式**：Base64

### 4.2 加密数据格式

```
Base64(IV + EncryptedData)
```

其中：

- IV: 16 字节随机初始化向量
- EncryptedData: AES 加密后的 JSON 数据

### 4.3 加密/解密流程

#### 加密流程

1. 生成 16 字节随机 IV
2. 使用 AES-256-CBC 加密 JSON 数据
3. 将 IV 和加密数据拼接
4. Base64 编码输出

#### 解密流程

1. Base64 解码输入数据
2. 提取前 16 字节作为 IV
3. 提取剩余数据作为加密内容
4. 使用 AES-256-CBC 解密
5. 解析 JSON 数据

### 4.4 密钥管理

- 每个 ProductVersion 拥有独立的加密密钥
- 密钥长度：32 字节（256 位）
- 密钥生成：使用加密安全的随机数生成器
- 密钥存储：数据库加密存储（可选）

## 5. 错误处理规范

### 5.1 标准错误码

| 错误码 | HTTP 状态码 | 说明             |
| ------ | ----------- | ---------------- |
| 1001   | 400         | 请求参数无效     |
| 1002   | 401         | License Key 无效 |
| 1003   | 403         | License 已撤销   |
| 1004   | 404         | License 不存在   |
| 1005   | 410         | License 已过期   |
| 1006   | 422         | 配置数据格式错误 |
| 1007   | 429         | 请求频率超限     |
| 5001   | 500         | 服务器内部错误   |
| 5002   | 503         | 服务暂时不可用   |

### 5.2 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": 1002,
    "message": "License Key无效",
    "details": "提供的License Key格式不正确或不存在"
  },
  "timestamp": **********
}
```
