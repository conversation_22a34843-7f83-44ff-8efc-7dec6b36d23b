import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3, Key, Package, ShoppingCart, Users } from "lucide-react"

const stats = [
  {
    title: "总产品数",
    value: "12",
    description: "活跃产品",
    icon: Package,
    trend: "+2.5%"
  },
  {
    title: "活跃许可证",
    value: "1,247",
    description: "已激活许可证",
    icon: Key,
    trend: "+12.3%"
  },
  {
    title: "本月订单",
    value: "89",
    description: "新增订单",
    icon: ShoppingCart,
    trend: "+8.7%"
  },
  {
    title: "管理员数",
    value: "8",
    description: "系统管理员",
    icon: Users,
    trend: "0%"
  }
]

export function Dashboard() {
  return (
    <div className="space-y-4 lg:space-y-6">
      <div>
        <h1 className="text-2xl lg:text-3xl font-bold">仪表板</h1>
        <p className="text-muted-foreground text-sm lg:text-base">系统概览和关键指标</p>
      </div>

      <div className="grid gap-4 lg:gap-6 grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xs lg:text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-3 w-3 lg:h-4 lg:w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-lg lg:text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
                <div className="mt-1 lg:mt-2 text-xs text-green-600">
                  {stat.trend} 较上月
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <div className="grid gap-4 lg:gap-6 grid-cols-1 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg lg:text-xl">最近活动</CardTitle>
            <CardDescription className="text-sm">
              系统最新的操作记录
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">
              暂无数据显示
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg lg:text-xl">快速操作</CardTitle>
            <CardDescription className="text-sm">
              常用功能快捷入口
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">
              功能开发中...
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}