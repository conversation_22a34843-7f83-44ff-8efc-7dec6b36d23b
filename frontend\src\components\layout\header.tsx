import { <PERSON><PERSON> } from "@/components/ui/button"
import { Menu } from "lucide-react"
import { ColorPicker } from "@/components/custom/color-picker"
import { ModeToggle } from "@/components/custom/mode-toggle"

interface HeaderProps {
  onMenuClick: () => void
  sidebarOpen: boolean
}

export function Header({ onMenuClick, sidebarOpen }: HeaderProps) {
  return (
    <header className="flex h-14 lg:h-16 items-center justify-between border-b bg-background px-4 lg:px-6">
      <div className="flex items-center gap-2 lg:gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={onMenuClick}
          className="lg:hidden"
        >
          <Menu className="h-5 w-5" />
        </Button>
        
        <h1 className="hidden lg:block text-xl font-semibold">许可证管理系统</h1>
      </div>

      <div className="flex items-center gap-2 lg:gap-4">
        <ColorPicker />
        <ModeToggle />
      </div>
    </header>
  )
}