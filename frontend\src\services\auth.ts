import { apiClient } from '../lib/client';
import { useAuthStore } from '@/stores/auth-store';
import type { LoginRequest, LoginResponse } from '@/types/api';

export class AuthService {
  // 登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/auth/login', credentials);
    
    if (response.success && response.data) {
      // 保存token到store
      const { setToken, setUser } = useAuthStore.getState();
      setToken(response.data.token);
      
      // 如果返回数据包含用户信息，也保存到store
      if (response.data.admin_id) {
        setUser({
          id: response.data.admin_id,
          username: response.data.username,
          role: response.data.role,
        });
      }
      
      return response.data;
    }
    
    throw new Error(response.message || '登录失败');
  }

  // 刷新token
  async refreshToken(): Promise<{ token: string; expires_in: number }> {
    const response = await apiClient.post<{ token: string; expires_in: number }>('/auth/refresh');
    
    if (response.success && response.data) {
      const { setToken } = useAuthStore.getState();
      setToken(response.data.token);
      return response.data;
    }
    
    throw new Error(response.message || 'Token刷新失败');
  }

  // 登出
  async logout(): Promise<void> {
    const { clearAuth } = useAuthStore.getState();
    
    try {
      await apiClient.post('/auth/logout');
    } finally {
      // 无论服务器响应如何，都清除本地认证状态
      clearAuth();
    }
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    const { isAuthenticated } = useAuthStore.getState();
    return isAuthenticated;
  }

  // 获取当前token
  getToken(): string | null {
    const { token } = useAuthStore.getState();
    return token;
  }

  // 获取当前用户信息
  getCurrentUser() {
    const { user } = useAuthStore.getState();
    return user;
  }
}

export const authService = new AuthService();