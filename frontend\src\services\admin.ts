import { apiClient } from '../lib/client';
import type {
  Admin,
  CreateAdminRequest,
  UpdateAdminRequest,
  AdminQueryParams,
  PaginatedResponse
} from '@/types/api';

export class AdminService {
  // 获取管理员列表
  async getAdmins(params?: AdminQueryParams): Promise<PaginatedResponse<Admin>> {
    const response = await apiClient.get<{
      admins: Admin[];
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }>('/admins', params);
    
    if (response.success && response.data) {
      return {
        data: response.data.admins,
        pagination: {
          page: response.data.page,
          limit: response.data.limit,
          total: response.data.total,
          totalPages: response.data.totalPages,
        },
      };
    }
    
    throw new Error(response.message || '获取管理员列表失败');
  }

  // 创建管理员
  async createAdmin(adminData: CreateAdminRequest): Promise<{ id: number }> {
    const response = await apiClient.post<{ id: number }>('/admins', adminData);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '创建管理员失败');
  }

  // 获取管理员详情
  async getAdmin(id: number): Promise<{ admin: Admin }> {
    const response = await apiClient.get<{ admin: Admin }>(`/admins/${id}`);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取管理员信息失败');
  }

  // 更新管理员
  async updateAdmin(id: number, updateData: UpdateAdminRequest): Promise<void> {
    const response = await apiClient.put(`/admins/${id}`, updateData);
    
    if (!response.success) {
      throw new Error(response.message || '更新管理员失败');
    }
  }

  // 删除管理员
  async deleteAdmin(id: number): Promise<void> {
    const response = await apiClient.delete(`/admins/${id}`);
    
    if (!response.success) {
      throw new Error(response.message || '删除管理员失败');
    }
  }
}

export const adminService = new AdminService();