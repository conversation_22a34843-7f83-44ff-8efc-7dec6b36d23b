import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "@/components/custom/theme-provider"
import { colorThemes } from "@/lib/colors"
import { toast } from "@/lib/toast"
import { Palette, Check } from "lucide-react"

export function ColorPicker() {
  const { colorTheme, setColorTheme } = useTheme()

  const handleColorThemeChange = (themeName: string) => {
    setColorTheme(themeName)
    toast.success("主题已切换", {
      description: "颜色主题应用成功"
    })
  }


  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Palette className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64" align="end">
        <DropdownMenuLabel>色彩主题</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <div className="flex flex-wrap gap-2 p-2">
          {colorThemes.map((theme) => (
            <div
              key={theme.name}
              className="relative cursor-pointer"
              onClick={() => handleColorThemeChange(theme.name)}
            >
              <div 
                className="w-8 h-8 rounded-full border-2 border-border hover:border-primary transition-colors"
                style={{ backgroundColor: theme.colors.light.primary }}
              />
              {colorTheme === theme.name && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Check className="h-4 w-4 text-white drop-shadow-md" />
                </div>
              )}
            </div>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}