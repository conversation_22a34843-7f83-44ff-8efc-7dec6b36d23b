import { createContext, useContext, useEffect } from "react"
import { useUIStore } from "@/stores/ui-store"
import { applyColorTheme } from "@/lib/colors"

type ThemeProviderProps = {
  children: React.ReactNode
}

type ThemeProviderState = {
  theme: 'light' | 'dark' | 'system'
  colorTheme: string
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setColorTheme: (colorTheme: string) => void
}

const initialState: ThemeProviderState = {
  theme: "system",
  colorTheme: "neutral",
  setTheme: () => null,
  setColorTheme: () => null,
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const { theme, colorTheme, setTheme, setColorTheme } = useUIStore()

  useEffect(() => {
    const root = window.document.documentElement

    root.classList.remove("light", "dark")

    let effectiveTheme: "light" | "dark"
    if (theme === "system") {
      effectiveTheme = window.matchMedia("(prefers-color-scheme: dark)")
        .matches
        ? "dark"
        : "light"
    } else {
      effectiveTheme = theme
    }

    root.classList.add(effectiveTheme)
    applyColorTheme(colorTheme, effectiveTheme)
  }, [theme, colorTheme])

  const value = {
    theme,
    colorTheme,
    setTheme,
    setColorTheme,
  }

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider")

  return context
}