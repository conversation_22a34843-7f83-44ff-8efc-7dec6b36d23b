import { toast } from "sonner";
import type { ApiResponse, ApiError } from "@/types/api";
import { useAuthStore } from "@/stores/auth-store";

// API配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// API错误类
export class ApiException extends Error {
  constructor(
    public message: string,
    public status: number,
    public response?: ApiError
  ) {
    super(message);
    this.name = 'ApiException';
  }
}

// 基础API客户端
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  // 获取认证store实例
  private getAuthStore() {
    return useAuthStore.getState();
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    // 设置默认headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string> || {}),
    };

    // 添加认证token
    const store = this.getAuthStore();
    const token = store.token;
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data: ApiResponse<T> = await response.json();

      // 处理认证错误
      if (response.status === 401) {
        // Token过期，尝试刷新
        const refreshResult = await this.tryRefreshToken();
        if (refreshResult) {
          // 重试原请求
          return this.request(endpoint, options);
        } else {
          // 刷新失败，清除token并跳转登录
          store.clearAuth();
          window.location.href = '/login';
          throw new ApiException('认证已过期，请重新登录', 401, data as ApiError);
        }
      }

      // 处理其他错误
      if (!response.ok || !data.success) {
        throw new ApiException(
          data.message || '请求失败',
          response.status,
          data as ApiError
        );
      }

      return data;
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }

      // 网络错误或其他异常
      console.error('API request failed:', error);
      throw new ApiException(
        '网络请求失败，请检查网络连接',
        0
      );
    }
  }

  private async tryRefreshToken(): Promise<boolean> {
    const store = this.getAuthStore();
    const token = store.token;
    if (!token) {
      return false;
    }

    try {
      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data: ApiResponse<{ token: string; expires_in: number }> = await response.json();
        if (data.success && data.data?.token) {
          store.setToken(data.data.token);
          return true;
        }
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    return false;
  }

  // HTTP方法封装
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const searchParams = params ? new URLSearchParams(
      Object.entries(params).filter(([, value]) => value != null)
        .map(([key, value]) => [key, String(value)])
    ).toString() : '';
    
    const url = searchParams ? `${endpoint}?${searchParams}` : endpoint;
    return this.request<T>(url, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// 创建全局API客户端实例
export const apiClient = new ApiClient();

// 错误处理工具函数
export const handleApiError = (error: unknown, showToast: boolean = true): string => {
  let message = '未知错误';

  if (error instanceof ApiException) {
    message = error.message;
  } else if (error instanceof Error) {
    message = error.message;
  }

  if (showToast) {
    toast.error(message);
  }

  return message;
};