# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a software license verification and distribution management system built on Cloudflare Workers. It provides license validation services with a two-tier distribution architecture: developers/publishers (admins) and resellers (distributors).

## Architecture

- **Backend**: Cloudflare Workers (TypeScript) with Hono framework and D1 database
- **Frontend**: React 19 + TypeScript with Vite build system
- **Database**: Cloudflare D1 (SQLite) with Drizzle ORM
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: Zustand for client state
- **Forms**: react-hook-form with Zod validation
- **Deployment**: Wrangler for Cloudflare Workers

## Development Commands

### Backend Commands

Navigate to the `backend/` directory for backend development:

```bash
cd backend
```

- `pnpm run dev` - Start development server with hot reloading
- `pnpm run deploy` - Deploy to Cloudflare Workers
- `pnpm run test` - Run tests with Vitest
- `pnpm run cf-typegen` - Generate TypeScript types for Cloudflare bindings
- `pnpm run db:generate` - Generate Drizzle migration files
- `pnpm run db:migrate` - Run database migrations
- `pnpm run db:push` - Push schema changes to database
- `pnpm run db:studio` - Open Drizzle Studio for database management
- `pnpm run db:check` - Check database schema consistency

### Frontend Commands

Navigate to the `frontend/` directory for frontend development:

```bash
cd frontend
```

- `pnpm run dev` - Start Vite development server with hot reloading
- `pnpm run build` - Build production bundle (TypeScript compilation + Vite build)
- `pnpm run lint` - Run ESLint
- `pnpm run preview` - Preview production build locally

### Database Commands

Database operations are handled through Drizzle ORM and Wrangler:

```bash
# Database schema management (in backend directory)
pnpm run db:generate  # Generate new migrations
pnpm run db:migrate   # Apply migrations to D1
pnpm run db:push      # Push schema changes directly

# Drizzle Studio for database visualization
pnpm run db:studio    # Open database management UI
```

### Environment Variables

Backend environment variables (configured in Wrangler):
- `JWT_SECRET` - JWT signing secret
- `CLOUDFLARE_ACCOUNT_ID` - Cloudflare account ID for D1 access
- `CLOUDFLARE_DATABASE_ID` - D1 database ID
- `CLOUDFLARE_D1_TOKEN` - Cloudflare D1 API token

## Database Schema

### Primary Tables (D1)

- `users` - User accounts (admins and distributors) with role-based permissions
- `products` - Software products (base information)
- `product_versions` - Product versions with pricing, verification templates, cover images, and encryption keys
- `authorizations` - Distributor product authorization and custom pricing
- `licenses` - License keys with encrypted verification instances

## Configuration

### Backend Configuration

- **Wrangler**: D1 database binding, assets directory, observability enabled
- **Drizzle**: SQLite schema management with migrations directory
- **Crypto**: AES-256-CBC encryption service for sensitive data

### Frontend Configuration

- **Vite Config**: React plugin with Tailwind CSS, path aliases (`@` → `./src`)
- **Component Libraries**: shadcn/ui with Radix UI primitives
- **Routing**: React Router DOM for client-side navigation
- **ESLint**: Modern TypeScript ESLint configuration with React support

## Frontend Architecture

### Key Components Structure

- **Layout Components**: `components/layout/` - Header, Sidebar, main Layout wrapper
- **UI Components**: `components/ui/` - shadcn/ui components (Button, Card, Form, etc.)
- **Custom Components**: `components/custom/` - Theme provider, color picker, mode toggle
- **Pages**: `pages/` - Dashboard, Login pages with React Router integration
- **Services**: `services/` - API client functions for backend communication
- **Stores**: `stores/` - Zustand stores for auth and UI state management
- **Types**: `types/api.ts` - TypeScript definitions for API requests/responses

### State Management

- **Auth Store**: User authentication state, token management, auto-initialization
- **UI Store**: Theme, sidebar state, and other UI preferences
- **API Client**: Centralized HTTP client with error handling and type safety

## Backend Architecture

### Current Implementation

- **Entry Point**: `src/index.ts` - Basic Cloudflare Worker setup
- **Router**: Hono framework for route handling
- **Database**: Drizzle ORM with SQLite schema, D1 bindings
- **Crypto**: Custom AES-256-CBC encryption service in `src/utils/crypto.ts`
- **Testing**: Vitest with Cloudflare Workers pool

### Database Schema

Comprehensive schema with proper indexing in `src/db/schema.ts`:
- User management with role-based access
- Product catalog with versioning
- Authorization system for distributor permissions
- License management with encrypted verification data
- Optimized indexes for performance and querying

## API Structure (Planned Implementation)

### Client APIs (Public)

- `POST /verify` - License verification endpoint

### Admin APIs (Authenticated)

- Authentication: `POST /admin/login`, `POST /admin/refresh`
- Product management: CRUD operations (admin only)
- License management: Generate, list, revoke, view details
- Distributor management: Create/manage distributors (admin only)
- Statistics: Sales and usage analytics

### Distributor APIs (Authenticated)

- Authentication: `POST /distributor/login`
- Licensed product management: View authorized products
- License generation: Generate licenses for authorized products
- Sales statistics: Personal sales analytics

## Business Logic

### User Roles

- **Admin**: Full system access, product management, distributor creation and authorization
- **Distributor**: Limited to authorized products, license generation, personal statistics

### Verification System

- Template-based verification configuration per product version
- AES-256-CBC encryption for sensitive verification instances
- Custom crypto service in `src/utils/crypto.ts` for secure data handling
- Real-time verification without caching for immediate consistency
- Support for expiration dates, device limits, feature toggles, etc.

### Distribution Model

1. Admin creates products and versions with verification templates and encryption keys
2. Admin authorizes distributors to sell specific product versions
3. Distributors can set custom pricing for their authorized products
4. Distributors generate licenses with encrypted verification instances
5. End-users verify licenses in real-time via the verification endpoint

## TypeScript Configuration

The project uses modern TypeScript with:

- ESNext target and modules
- Bundler module resolution
- Strict type checking enabled
- Hono JSX integration
- Cloudflare Workers types via `wrangler types`
- Drizzle generated types for database operations

## Key Integration Points

When implementing features:

1. Use Drizzle ORM for all database operations via D1
2. All verification requests query D1 directly for real-time consistency
3. Implement proper role-based authorization checks (ADMIN/DISTRIBUTOR)
4. Use the CryptoService for sensitive data encryption and decryption
5. Follow the distributor authorization model for access control
6. Maintain proper indexing and query optimization patterns
