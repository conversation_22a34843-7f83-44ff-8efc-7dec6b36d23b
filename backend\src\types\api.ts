export interface ConfigTemplate {
  [key: string]: any;
}

export interface VerifyConfig {
  [key: string]: any;
}

export interface GetConfigResponse {
  configTemplate: ConfigTemplate;
  verifyConfig: VerifyConfig;
  version: {
    id: number;
    version: string;
    versionName: string;
  };
  product: {
    id: number;
    name: string;
    description: string;
  };
}

export interface UpdateConfigRequest {
  verifyConfig: VerifyConfig;
}

export interface UpdateConfigResponse {
  updated: boolean;
  verifyConfig: any;
}

export interface StandardResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: number;
    message: string;
    details?: string;
  };
  timestamp: number;
}

// 许可证相关响应类型
export interface GetLicenseWithVersionResponse {
  id: number;
  licenseKey: string;
  status: string;
  verifyConfig?: string;
  activatedAt?: string;
  distributorId: number;
  createdAt: string;
  updatedAt: string;
  version: {
    id: number;
    version: string;
    versionName?: string;
    description?: string;
    configTemplate: string;
    encryptionKey: string;
    defaultPrice: number;
    downloadLink?: string;
    coverUrl?: string;
    changelog?: string;
    status: string;
    productId: number;
    createdAt: string;
    updatedAt: string;
    product: {
      id: number;
      name: string;
      description?: string;
      category?: string;
      status: string;
      createdAt: string;
      updatedAt: string;
    };
  };
}