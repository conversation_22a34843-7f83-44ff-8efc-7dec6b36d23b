// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface ApiError {
  success: false;
  message: string;
  data: null;
}

export interface ApiSuccess<T = any> {
  success: true;
  message: string;
  data?: T;
}

// Auth Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  admin_id: number;
  username: string;
  role: string;
  authorized_products: string[];
  expires_in: number;
}

// Admin Types
export interface Admin {
  id: number;
  username: string;
  role: 'super' | 'normal';
  status: 'active' | 'inactive';
  product_ids?: string;
  created_at: string;
  updated_at: string;
  assigned_products?: Product[];
}

export interface CreateAdminRequest {
  username: string;
  password: string;
  role: 'super' | 'normal';
  product_ids?: number[];
}

export interface UpdateAdminRequest {
  password?: string;
  role?: 'super' | 'normal';
  product_ids?: number[];
  status?: 'active' | 'inactive';
}

// Product Types
export interface Product {
  id: number;
  name: string;
  description?: string;
  verification_strategy: 'expiration' | 'device_count' | 'feature_based';
  max_devices?: number;
  features?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface CreateProductRequest {
  name: string;
  description?: string;
  verification_strategy: 'expiration' | 'device_count' | 'feature_based';
  max_devices?: number;
  features?: string[];
}

export interface UpdateProductRequest {
  name?: string;
  description?: string;
  verification_strategy?: 'expiration' | 'device_count' | 'feature_based';
  max_devices?: number;
  features?: string[];
  status?: 'active' | 'inactive';
}

// License Types
export interface License {
  id: number;
  product_id: number;
  license_key: string;
  status: 'active' | 'expired' | 'revoked';
  expires_at?: string;
  max_devices?: number;
  admin_id: number;
  created_at: string;
  updated_at: string;
  product_name?: string;
}

export interface CreateLicenseRequest {
  product_id: number;
  count: number;
  expires_at?: string;
  max_devices?: number;
}

export interface UpdateLicenseRequest {
  status?: 'active' | 'expired' | 'revoked';
  expires_at?: string;
  max_devices?: number;
}

export interface LicenseDetail {
  license: License;
  devices: Device[];
  verification_logs: VerificationLog[];
}

// Device Types
export interface Device {
  id: number;
  license_id: number;
  device_id: string;
  device_info?: string;
  last_verification: string;
  created_at: string;
  updated_at: string;
}

// Order Types
export interface Order {
  id: number;
  admin_id: number;
  product_id: number;
  license_count: number;
  unit_price: number;
  total_price: number;
  status: 'pending' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
  product_name?: string;
  admin_username?: string;
}

export interface CreateOrderRequest {
  product_id: number;
  license_count: number;
  unit_price: number;
}

export interface UpdateOrderRequest {
  status: 'pending' | 'completed' | 'cancelled';
}

// Statistics Types
export interface SalesStats {
  summary: {
    total_orders: number;
    total_licenses: number;
    total_revenue: number;
    period: string;
  };
  by_product: Array<{
    product_name: string;
    orders: number;
    licenses: number;
    revenue: number;
  }>;
  trend: Array<{
    date: string;
    orders: number;
    licenses: number;
    revenue: number;
  }>;
}

export interface VerificationStats {
  summary: {
    total_verifications: number;
    successful_verifications: number;
    failed_verifications: number;
    success_rate: number;
    period: string;
  };
  by_result: Array<{
    result: string;
    count: number;
  }>;
  trend: Array<{
    date: string;
    total_verifications: number;
    successful_verifications: number;
  }>;
}

export interface DashboardStats {
  licenses: {
    total: number;
    active: number;
    expired: number;
    revoked: number;
  };
  orders_this_month: {
    total: number;
    revenue: number;
    licenses_sold: number;
  };
  verifications_today: {
    total: number;
    successful: number;
  };
  active_devices_week: number;
}

// Verification Log Types
export interface VerificationLog {
  id: number;
  license_key: string;
  device_id?: string;
  result: 'success' | 'failed';
  reason?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationResponse;
}

// Query Parameters
export interface AdminQueryParams extends PaginationParams {
  role?: 'super' | 'normal';
  status?: 'active' | 'inactive';
  search?: string;
}

export interface ProductQueryParams extends PaginationParams {
  status?: 'active' | 'inactive';
  search?: string;
}

export interface LicenseQueryParams extends PaginationParams {
  product_id?: number;
  status?: 'active' | 'expired' | 'revoked';
  search?: string;
}

export interface OrderQueryParams extends PaginationParams {
  status?: 'pending' | 'completed' | 'cancelled';
  product_id?: number;
  start_date?: string;
  end_date?: string;
}

export interface StatsQueryParams {
  period?: 'day' | 'week' | 'month' | 'year';
  start_date?: string;
  end_date?: string;
}