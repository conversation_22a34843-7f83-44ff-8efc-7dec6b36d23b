import { StandardResponse } from '../types/api';

export const createResponse = <T>(data: T, success = true): StandardResponse<T> => {
  return {
    success,
    data,
    timestamp: Date.now()
  };
};

export const createErrorResponse = (code: number, message: string, details?: string): StandardResponse<null> => {
  return {
    success: false,
    error: {
      code,
      message,
      details
    },
    timestamp: Date.now()
  };
};

export const successResponse = <T>(data: T): StandardResponse<T> => {
  return createResponse(data, true);
};

export const errorResponse = (code: number, message: string, details?: string): StandardResponse<null> => {
  return createErrorResponse(code, message, details);
};