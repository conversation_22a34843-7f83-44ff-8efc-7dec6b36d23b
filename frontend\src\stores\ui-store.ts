import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface UIState {
  // 侧边栏状态
  sidebarOpen: boolean
  isMobile: boolean
  setSidebarOpen: (open: boolean) => void
  setIsMobile: (mobile: boolean) => void
  toggleSidebar: () => void
  
  // 主题状态
  theme: 'light' | 'dark' | 'system'
  colorTheme: string
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setColorTheme: (colorTheme: string) => void
}

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // 侧边栏初始状态 - 会被persist覆盖或根据屏幕尺寸初始化
      sidebarOpen: true,
      isMobile: false,
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      setIsMobile: (mobile) => {
        const currentState = get()
        set({ isMobile: mobile })
        
        // 如果是首次检测到屏幕尺寸变化，且localStorage中没有保存侧边栏状态
        // 则根据设备类型设置默认状态
        const savedState = localStorage.getItem('verify-ui-storage')
        if (!savedState || !JSON.parse(savedState).state?.sidebarOpen === undefined) {
          set({ sidebarOpen: !mobile }) // PC展开，移动端收起
        }
      },
      toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
      
      // 主题初始状态
      theme: 'system',
      colorTheme: 'neutral',
      setTheme: (theme) => set({ theme }),
      setColorTheme: (colorTheme) => set({ colorTheme }),
    }),
    {
      name: 'verify-ui-storage',
      // 持久化所有UI状态
      partialize: (state) => ({
        theme: state.theme,
        colorTheme: state.colorTheme,
        sidebarOpen: state.sidebarOpen,
      }),
    }
  )
)