import crypto from 'crypto';

export class CryptoService {
  /**
   * AES-256-CBC 加密
   * @param data 要加密的数据
   * @param key 加密密钥
   * @returns Base64编码的加密数据 (IV + 加密数据)
   */
  encrypt(data: string, key: string): string {
    try {
      // 生成随机 IV (16字节)
      const iv = crypto.getRandomValues(new Uint8Array(16));
      
      // 将密钥转换为32字节的Buffer
      const keyBuffer = this.stringToKey(key);
      
      // 创建加密器
      const cipher = crypto.createCipheriv('aes-256-cbc', keyBuffer, iv);
      
      // 加密数据
      let encrypted = cipher.update(data, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      // 拼接 IV 和加密数据，然后进行 Base64 编码
      const combined = new Uint8Array(iv.length + Buffer.from(encrypted, 'base64').length);
      combined.set(iv);
      combined.set(Buffer.from(encrypted, 'base64'), iv.length);
      
      return Buffer.from(combined).toString('base64');
    } catch (error) {
      throw new Error(`加密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * AES-256-CBC 解密
   * @param encryptedData 加密的数据 (Base64编码的 IV + 加密数据)
   * @param key 解密密钥
   * @returns 解密后的原始数据
   */
  decrypt(encryptedData: string, key: string): string {
    try {
      // 将 Base64 数据解码为 Buffer
      const combined = Buffer.from(encryptedData, 'base64');
      
      // 提取前16字节作为 IV
      const iv = combined.slice(0, 16);
      
      // 提取剩余数据作为加密内容
      const encrypted = combined.slice(16);
      
      // 将密钥转换为32字节的Buffer
      const keyBuffer = this.stringToKey(key);
      
      // 创建解密器
      const decipher = crypto.createDecipheriv('aes-256-cbc', keyBuffer, iv);
      
      // 解密数据
      let decrypted = decipher.update(encrypted, undefined, 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      throw new Error(`解密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 将字符串密钥转换为32字节的Buffer
   * @param key 字符串密钥
   * @returns 32字节的Buffer
   */
  private stringToKey(key: string): Buffer {
    // 如果密钥长度已经是32字节，直接返回
    if (Buffer.from(key).length === 32) {
      return Buffer.from(key);
    }
    
    // 使用 SHA-256 哈希生成32字节的密钥
    return crypto.createHash('sha256').update(key).digest();
  }

  /**
   * 生成随机加密密钥
   * @returns 32字节的随机密钥 (Base64编码)
   */
  generateRandomKey(): string {
    return crypto.getRandomValues(new Uint8Array(32)).toString('base64');
  }

  /**
   * 验证密钥格式
   * @param key 要验证的密钥
   * @returns 密钥是否有效
   */
  validateKey(key: string): boolean {
    try {
      const keyBuffer = this.stringToKey(key);
      return keyBuffer.length === 32;
    } catch {
      return false;
    }
  }
}

// 导出单例
export const cryptoService = new CryptoService();