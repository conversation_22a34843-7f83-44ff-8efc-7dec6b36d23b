{"version": "6", "dialect": "sqlite", "id": "0d6b715e-36fb-406e-9a38-7966c766af1d", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"authorizations": {"name": "authorizations", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "distributorId": {"name": "distributorId", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "versionId": {"name": "versionId", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "customPrice": {"name": "customPrice", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'ACTIVE'"}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"authorizations_distributorId_idx": {"name": "authorizations_distributorId_idx", "columns": ["distributorId"], "isUnique": false}, "authorizations_versionId_idx": {"name": "authorizations_versionId_idx", "columns": ["versionId"], "isUnique": false}, "authorizations_status_idx": {"name": "authorizations_status_idx", "columns": ["status"], "isUnique": false}, "authorizations_createdAt_idx": {"name": "authorizations_createdAt_idx", "columns": ["createdAt"], "isUnique": false}, "authorizations_distributorId_status_idx": {"name": "authorizations_distributorId_status_idx", "columns": ["distributorId", "status"], "isUnique": false}, "authorizations_distributorId_versionId_key": {"name": "authorizations_distributorId_versionId_key", "columns": ["distributorId", "versionId"], "isUnique": true}}, "foreignKeys": {"authorizations_distributorId_users_id_fk": {"name": "authorizations_distributorId_users_id_fk", "tableFrom": "authorizations", "tableTo": "users", "columnsFrom": ["distributorId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "authorizations_versionId_product_versions_id_fk": {"name": "authorizations_versionId_product_versions_id_fk", "tableFrom": "authorizations", "tableTo": "product_versions", "columnsFrom": ["versionId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "licenses": {"name": "licenses", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "versionId": {"name": "versionId", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "licenseKey": {"name": "licenseKey", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'INACTIVE'"}, "verifyConfig": {"name": "verifyConfig", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "activatedAt": {"name": "activatedAt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "distributorId": {"name": "distributorId", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"licenses_licenseKey_key": {"name": "licenses_licenseKey_key", "columns": ["licenseKey"], "isUnique": true}, "licenses_licenseKey_idx": {"name": "licenses_licenseKey_idx", "columns": ["licenseKey"], "isUnique": false}, "licenses_versionId_idx": {"name": "licenses_versionId_idx", "columns": ["versionId"], "isUnique": false}, "licenses_distributorId_idx": {"name": "licenses_distributorId_idx", "columns": ["distributorId"], "isUnique": false}, "licenses_status_idx": {"name": "licenses_status_idx", "columns": ["status"], "isUnique": false}, "licenses_activatedAt_idx": {"name": "licenses_activatedAt_idx", "columns": ["activatedAt"], "isUnique": false}, "licenses_createdAt_idx": {"name": "licenses_createdAt_idx", "columns": ["createdAt"], "isUnique": false}, "licenses_distributorId_status_idx": {"name": "licenses_distributorId_status_idx", "columns": ["distributorId", "status"], "isUnique": false}, "licenses_status_createdAt_idx": {"name": "licenses_status_createdAt_idx", "columns": ["status", "createdAt"], "isUnique": false}}, "foreignKeys": {"licenses_versionId_product_versions_id_fk": {"name": "licenses_versionId_product_versions_id_fk", "tableFrom": "licenses", "tableTo": "product_versions", "columnsFrom": ["versionId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "licenses_distributorId_users_id_fk": {"name": "licenses_distributorId_users_id_fk", "tableFrom": "licenses", "tableTo": "users", "columnsFrom": ["distributorId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "product_versions": {"name": "product_versions", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "productId": {"name": "productId", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "versionName": {"name": "versionName", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "configTemplate": {"name": "configTemplate", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "encryptionKey": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "defaultPrice": {"name": "defaultPrice", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "downloadLink": {"name": "downloadLink", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "coverUrl": {"name": "coverUrl", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "changelog": {"name": "changelog", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'ACTIVE'"}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"product_versions_productId_idx": {"name": "product_versions_productId_idx", "columns": ["productId"], "isUnique": false}, "product_versions_status_idx": {"name": "product_versions_status_idx", "columns": ["status"], "isUnique": false}, "product_versions_createdAt_idx": {"name": "product_versions_createdAt_idx", "columns": ["createdAt"], "isUnique": false}, "product_versions_productId_status_idx": {"name": "product_versions_productId_status_idx", "columns": ["productId", "status"], "isUnique": false}, "product_versions_productId_version_key": {"name": "product_versions_productId_version_key", "columns": ["productId", "version"], "isUnique": true}}, "foreignKeys": {"product_versions_productId_products_id_fk": {"name": "product_versions_productId_products_id_fk", "tableFrom": "product_versions", "tableTo": "products", "columnsFrom": ["productId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "products": {"name": "products", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'ACTIVE'"}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"products_name_key": {"name": "products_name_key", "columns": ["name"], "isUnique": true}, "products_status_idx": {"name": "products_status_idx", "columns": ["status"], "isUnique": false}, "products_category_idx": {"name": "products_category_idx", "columns": ["category"], "isUnique": false}, "products_createdAt_idx": {"name": "products_createdAt_idx", "columns": ["createdAt"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "passwordHash": {"name": "passwordHash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'ACTIVE'"}, "nickName": {"name": "nick<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "wechat": {"name": "wechat", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"users_username_key": {"name": "users_username_key", "columns": ["username"], "isUnique": true}, "users_role_idx": {"name": "users_role_idx", "columns": ["role"], "isUnique": false}, "users_status_idx": {"name": "users_status_idx", "columns": ["status"], "isUnique": false}, "users_createdAt_idx": {"name": "users_createdAt_idx", "columns": ["createdAt"], "isUnique": false}, "users_role_status_idx": {"name": "users_role_status_idx", "columns": ["role", "status"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}