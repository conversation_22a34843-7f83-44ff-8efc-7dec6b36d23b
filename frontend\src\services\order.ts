import { apiClient } from '../lib/client';
import type {
  Order,
  CreateOrderRequest,
  UpdateOrderRequest,
  OrderQueryParams,
  PaginatedResponse
} from '@/types/api';

export class OrderService {
  // 获取订单列表
  async getOrders(params?: OrderQueryParams): Promise<PaginatedResponse<Order>> {
    const response = await apiClient.get<{
      orders: Order[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>('/orders', params);
    
    if (response.success && response.data) {
      return {
        data: response.data.orders,
        pagination: response.data.pagination,
      };
    }
    
    throw new Error(response.message || '获取订单列表失败');
  }

  // 创建订单
  async createOrder(orderData: CreateOrderRequest): Promise<{ id: number; total_price: number }> {
    const response = await apiClient.post<{ id: number; total_price: number }>('/orders', orderData);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '创建订单失败');
  }

  // 获取订单详情
  async getOrder(id: number): Promise<{ order: Order }> {
    const response = await apiClient.get<{ order: Order }>(`/orders/${id}`);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取订单信息失败');
  }

  // 更新订单状态
  async updateOrder(id: number, updateData: UpdateOrderRequest): Promise<void> {
    const response = await apiClient.put(`/orders/${id}`, updateData);
    
    if (!response.success) {
      throw new Error(response.message || '更新订单失败');
    }
  }

  // 删除订单
  async deleteOrder(id: number): Promise<void> {
    const response = await apiClient.delete(`/orders/${id}`);
    
    if (!response.success) {
      throw new Error(response.message || '删除订单失败');
    }
  }
}

export const orderService = new OrderService();