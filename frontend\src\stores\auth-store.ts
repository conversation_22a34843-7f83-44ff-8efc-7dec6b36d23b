import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface AuthState {
  // 认证状态
  token: string | null
  isAuthenticated: boolean
  user: { id?: number; username?: string; role?: string } | null
  
  // 认证操作
  setToken: (token: string) => void
  setUser: (user: { id?: number; username?: string; role?: string } | null) => void
  clearAuth: () => void
  initializeAuth: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 认证初始状态
      token: null,
      isAuthenticated: false,
      user: null,
      
      setToken: (token) => {
        set({ token, isAuthenticated: true })
        // 同时更新localStorage
        localStorage.setItem('auth_token', token)
      },
      
      setUser: (user) => set({ user }),
      
      clearAuth: () => {
        set({ 
          token: null, 
          isAuthenticated: false, 
          user: null 
        })
        // 清除localStorage
        localStorage.removeItem('auth_token')
      },
      
      initializeAuth: () => {
        // 从localStorage恢复token
        const token = localStorage.getItem('auth_token')
        if (token) {
          set({ 
            token, 
            isAuthenticated: true 
          })
        }
      },
    }),
    {
      name: 'verify-auth-storage',
      // 持久化认证相关状态
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)