{"name": "backend", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:check": "drizzle-kit check"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "@types/node": "^22.17.0", "dotenv": "^16.4.7", "drizzle-kit": "^0.31.4", "tsx": "^4.19.2", "typescript": "^5.5.2", "wrangler": "^4.27.0"}, "dependencies": {"drizzle-orm": "^0.44.4", "hono": "^4.8.10"}}